<?php
/**
 * Test simple de validation sans AJAX
 */

session_start();
require_once 'pos_config.php';

echo "<h1>🧪 Test Validation Simple</h1>";

$pos = new POSConfig();

// Traitement POST
if ($_POST['action'] ?? '' === 'test_validation') {
    echo "<h2>🔄 Traitement de la validation...</h2>";
    
    // Vider et ajouter un article
    $pos->clearCart();
    
    // Récupérer un article
    $sql = "SELECT TOP 1 IDarticle, designation, prix, IDCategorie, Cuisine FROM articles WHERE prix > 0";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $article = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($article) {
        $pos->addToCart($article['IDarticle'], 1);
        echo "✅ Article ajouté: " . $article['designation'] . "<br>";
        
        // Tenter la validation
        $orderId = $pos->processOrder('cash');
        
        if ($orderId) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "🎉 <strong>SUCCÈS!</strong> Commande validée avec ID: $orderId";
            echo "</div>";
            
            // Vérifier en base
            $sql = "SELECT * FROM tickets WHERE IDtickets = ?";
            $stmt = $pos->pdo->prepare($sql);
            $stmt->execute([$orderId]);
            $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($ticket) {
                echo "<h3>📋 Détails du ticket créé:</h3>";
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                foreach ($ticket as $key => $value) {
                    echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
                }
                echo "</table>";
            }
            
            // Vérifier VteJour
            $sql = "SELECT * FROM VteJour WHERE IDtickets = ?";
            $stmt = $pos->pdo->prepare($sql);
            $stmt->execute([$orderId]);
            $details = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>📦 Détails des articles (" . count($details) . " lignes):</h3>";
            if (!empty($details)) {
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>Article</th><th>Quantité</th><th>Total</th><th>IDarticle</th></tr>";
                foreach ($details as $detail) {
                    echo "<tr>";
                    echo "<td>{$detail['articles']}</td>";
                    echo "<td>{$detail['quantite']}</td>";
                    echo "<td>{$detail['total']}</td>";
                    echo "<td>{$detail['IDarticle']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ <strong>ÉCHEC!</strong> Impossible de valider la commande";
            echo "</div>";
        }
        
        // État du panier après
        $cartAfter = $pos->getCart();
        echo "<h3>🛒 État du panier après validation:</h3>";
        if (empty($cartAfter)) {
            echo "✅ Panier vide<br>";
        } else {
            echo "⚠️ Panier contient encore " . count($cartAfter) . " article(s)<br>";
        }
        
    } else {
        echo "❌ Aucun article trouvé<br>";
    }
}

// Afficher le formulaire
?>

<h2>🎯 Test de validation</h2>
<p>Ce test va :</p>
<ol>
    <li>Vider le panier</li>
    <li>Ajouter un article</li>
    <li>Valider la commande</li>
    <li>Vérifier le résultat</li>
</ol>

<form method="POST">
    <input type="hidden" name="action" value="test_validation">
    <button type="submit" style="padding: 15px 30px; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
        🚀 Lancer le test de validation
    </button>
</form>

<h2>📊 État actuel</h2>
<?php
$cart = $pos->getCart();
echo "Panier actuel: " . count($cart) . " article(s)<br>";

if (!empty($cart)) {
    echo "<ul>";
    foreach ($cart as $id => $item) {
        echo "<li>Article: {$item['article']['designation']}, Qté: {$item['quantity']}, Prix: {$item['price']}</li>";
    }
    echo "</ul>";
}

// Derniers tickets
echo "<h3>🎫 Derniers tickets créés:</h3>";
try {
    $sql = "SELECT TOP 5 IDtickets, NumTick, DATE, heure, total FROM tickets ORDER BY IDtickets DESC";
    $stmt = $pos->pdo->prepare($sql);
    $stmt->execute();
    $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($tickets)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>NumTick</th><th>Date</th><th>Heure</th><th>Total</th></tr>";
        foreach ($tickets as $ticket) {
            echo "<tr>";
            echo "<td>{$ticket['IDtickets']}</td>";
            echo "<td>{$ticket['NumTick']}</td>";
            echo "<td>{$ticket['DATE']}</td>";
            echo "<td>{$ticket['heure']}</td>";
            echo "<td>{$ticket['total']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Aucun ticket trouvé<br>";
    }
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "<br>";
}
?>

<h2>📝 Logs récents</h2>
<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: scroll; font-family: monospace; font-size: 12px;">
<?php
$logFile = ini_get('error_log');
if ($logFile && file_exists($logFile)) {
    $lines = file($logFile);
    $recentLines = array_slice($lines, -30);
    foreach ($recentLines as $line) {
        if (strpos($line, 'DEBUG:') !== false || strpos($line, 'ERREUR') !== false) {
            echo htmlspecialchars($line) . "<br>";
        }
    }
} else {
    echo "Logs non accessibles";
}
?>
</div>

