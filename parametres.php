<?php
/**
 * Page Paramètres - Gestion du système POS
 */

require_once 'pos_config.php';

// Vérifier la connexion
if (!$pos->isConnected()) {
    die("Erreur de connexion à la base de données");
}

// Messages de feedback
$success_message = '';
$error_message = '';

if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paramètres - BeCoffe POS</title>
    
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }

        .logo i {
            color: #667eea;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .page-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title h1 {
            font-size: 36px;
            font-weight: 700;
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-title p {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .param-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .param-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .param-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            position: relative;
        }

        .param-card:nth-child(1) .card-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .param-card:nth-child(2) .card-icon {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .param-card:nth-child(3) .card-icon {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .param-card:nth-child(4) .card-icon {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
        }

        .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .card-description {
            font-size: 16px;
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .card-button {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .card-button:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-direction: column;
                gap: 10px;
                width: 100%;
            }

            .nav-link {
                justify-content: center;
            }

            .page-title h1 {
                font-size: 28px;
            }

            .cards-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .param-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="nav-links">
                <a href="pos_mobile.php" class="nav-link">
                    <i class="fas fa-cash-register"></i> Retour POS
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <!-- Titre de la page -->
        <div class="page-title">
            <h1><i class="fas fa-cogs"></i> Paramètres du système</h1>
            <p>Gérez les différents aspects de votre point de vente</p>
        </div>

        <!-- Grille des cartes -->
        <div class="cards-grid">
            <!-- Carte 1: Gestion des serveurs -->
            <div class="param-card">
                <div class="card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="card-title">Gestion des serveurs</h3>
                <p class="card-description">
                    Ajoutez, modifiez ou supprimez les serveurs. 
                    Gérez les permissions et les accès au système.
                </p>
                <button class="card-button" onclick="alert('Fonctionnalité en développement')">
                    <i class="fas fa-user-cog"></i>
                    Gérer les serveurs
                </button>
            </div>

            <!-- Carte 2: Gestion des produits -->
            <div class="param-card">
                <div class="card-icon">
                    <i class="fas fa-box"></i>
                </div>
                <h3 class="card-title">Gestion des produits</h3>
                <p class="card-description">
                    Ajoutez de nouveaux articles, modifiez les prix, 
                    gérez les stocks et les catégories.
                </p>
                <button class="card-button" onclick="alert('Fonctionnalité en développement')">
                    <i class="fas fa-edit"></i>
                    Gérer les produits
                </button>
            </div>

            <!-- Carte 3: Historique -->
            <div class="param-card">
                <div class="card-icon">
                    <i class="fas fa-history"></i>
                </div>
                <h3 class="card-title">Historique des ventes</h3>
                <p class="card-description">
                    Consultez l'historique complet des commandes, 
                    recherchez par période, serveur ou mode de paiement.
                </p>
                <a href="historique.php" class="card-button">
                    <i class="fas fa-search"></i>
                    Voir l'historique
                </a>
            </div>

            <!-- Carte 4: Statistiques -->
            <div class="param-card">
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="card-title">Analyses & Statistiques</h3>
                <p class="card-description">
                    Analysez les performances de vente, 
                    consultez les graphiques et les rapports détaillés.
                </p>
                <a href="analyses.php" class="card-button">
                    <i class="fas fa-analytics"></i>
                    Voir les analyses
                </a>
            </div>
        </div>
    </div>
</body>
</html>
